<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reports - Meat Management System</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
    <link rel="stylesheet" href="css/styles.css">
</head>
<body class="bg-gray-100" x-data="{ sidebarOpen: false }">
    <div class="flex h-screen">
        <!-- Sidebar -->
        <div :class="{'translate-x-0 ease-out': sidebarOpen, '-translate-x-full ease-in': !sidebarOpen}" class="fixed inset-y-0 left-0 z-30 w-64 bg-gray-800 text-white transform transition duration-300 lg:translate-x-0 lg:static lg:inset-0">
            <div class="p-4 border-b border-gray-700 flex justify-between items-center lg:justify-start">
                <div>
                    <h1 class="text-2xl font-bold">MeatMS</h1>
                    <p class="text-sm text-gray-400">Admin Panel</p>
                </div>
                <button @click="sidebarOpen = false" class="text-gray-400 hover:text-white lg:hidden">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
            <nav class="mt-4">
                <div class="px-4 py-2 text-gray-400 uppercase text-xs font-semibold">Main</div>
                <a href="index.html" class="block px-4 py-2 text-gray-300 hover:bg-gray-700">
                    <i class="fas fa-tachometer-alt mr-2"></i> Dashboard
                </a>
                
                <div class="px-4 py-2 text-gray-400 uppercase text-xs font-semibold mt-4">Management</div>
                <a href="products.html" class="block px-4 py-2 text-gray-300 hover:bg-gray-700">
                    <i class="fas fa-box mr-2"></i> Products
                </a>
                <a href="orders.html" class="block px-4 py-2 text-gray-300 hover:bg-gray-700">
                    <i class="fas fa-shopping-cart mr-2"></i> Orders
                </a>
                <a href="users.html" class="block px-4 py-2 text-gray-300 hover:bg-gray-700">
                    <i class="fas fa-users mr-2"></i> Users
                </a>
                <a href="vendors.html" class="block px-4 py-2 text-gray-300 hover:bg-gray-700">
                    <i class="fas fa-store mr-2"></i> Vendors
                </a>
                
                <div class="px-4 py-2 text-gray-400 uppercase text-xs font-semibold mt-4">Sales & Reports</div>
                <a href="sales.html" class="block px-4 py-2 text-gray-300 hover:bg-gray-700">
                    <i class="fas fa-chart-line mr-2"></i> Sales
                </a>
                <a href="reports.html" class="block px-4 py-2 bg-indigo-700 text-white rounded mx-2">
                    <i class="fas fa-file-alt mr-2"></i> Reports
                </a>
            </nav>
        </div>

        <!-- Overlay for mobile sidebar -->
        <div x-show="sidebarOpen" @click="sidebarOpen = false" class="fixed inset-0 z-20 bg-black opacity-50 transition-opacity lg:hidden" x-cloak></div>

        <!-- Main Content -->
        <div class="flex-1 flex flex-col overflow-hidden">
            <!-- Top Navigation -->
            <header class="bg-white shadow">
                <div class="flex items-center justify-between px-6 py-4">
                    <div class="flex items-center">
                        <button @click="sidebarOpen = !sidebarOpen" class="text-gray-500 focus:outline-none lg:hidden">
                            <i class="fas fa-bars text-xl"></i>
                        </button>
                        <h2 class="text-xl font-semibold text-gray-800 ml-2">Reports</h2>
                    </div>
                    <div class="flex items-center space-x-4">
                        <button class="p-2 text-gray-600 hover:text-gray-900">
                            <i class="fas fa-bell"></i>
                        </button>
                        <div class="relative">
                            <button class="flex items-center space-x-2 focus:outline-none">
                                <img class="w-8 h-8 rounded-full" src="https://via.placeholder.com/32" alt="User">
                                <span class="text-gray-700">Admin</span>
                                <i class="fas fa-chevron-down text-xs"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </header>

            <!-- Page Content -->
            <main class="flex-1 overflow-y-auto p-6">
                <!-- Page Header -->
                <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
                    <div>
                        <h1 class="text-2xl font-bold text-gray-900">Reports</h1>
                        <p class="mt-1 text-sm text-gray-600">Generate and view various system reports</p>
                    </div>
                    <div class="mt-4 md:mt-0">
                        <button class="bg-indigo-600 text-white px-4 py-2 rounded-lg hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            <i class="fas fa-plus mr-2"></i> Generate New Report
                        </button>
                    </div>
                </div>

                <!-- Reports Content Placeholder -->
                <div class="bg-white rounded-lg shadow p-6">
                    <h3 class="text-lg font-medium mb-4">Available Reports</h3>
                    <p class="text-gray-500">Report generation options and existing reports will be displayed here.</p>
                    <!-- Report generation form or list of reports will go here -->
                </div>
            </main>
        </div>
    </div>

    <script src="js/main.js"></script>
</body>
</html>
