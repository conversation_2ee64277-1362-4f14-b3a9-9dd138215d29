<?php
require_once 'database/conn.php';
require_once 'check_session.php';

// Require user to be logged in to access products
requireLogin();

// Optional: Require specific role for product management (uncomment if needed)
// requireRole('Staff'); // Staff and above can manage products

// Handle form submission for adding/editing products
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    try {
        // Check if this is an edit request
        if (isset($_POST['edit_product_id'])) {
            // Handle edit product
            $product_id = intval($_POST['edit_product_id']);
            $product = trim($_POST['edit_product'] ?? '');
            $category = trim($_POST['edit_category'] ?? '');
            $stock = filter_var($_POST['edit_stock'] ?? 0, FILTER_VALIDATE_INT);
            $price = filter_var($_POST['edit_price'] ?? 0.0, FILTER_VALIDATE_FLOAT);
            $status = trim($_POST['edit_status'] ?? '');
            $package = trim($_POST['edit_package'] ?? '');
            $description = trim($_POST['edit_description'] ?? '');

            // Server-side validation for edit
            $errors = [];
            if (empty($product)) $errors[] = 'Product name is required';
            if (empty($category)) $errors[] = 'Category is required';
            if ($stock === false || $stock < 0) $errors[] = 'Valid stock quantity is required';
            if ($price === false || $price < 0) $errors[] = 'Valid price is required';
            if (empty($status)) $errors[] = 'Status is required';

            // Validate status
            $valid_statuses = ['In Stock', 'Low Stock', 'Out of Stock'];
            if (!empty($status) && !in_array($status, $valid_statuses)) {
                $errors[] = 'Invalid status';
            }

            if (!empty($errors)) {
                $message = implode(', ', $errors);
                $messageType = 'error';
            } else {
                // Check for duplicate product name (excluding current product)
                $check_stmt = $pdo->prepare("SELECT id FROM products WHERE product = ? AND id != ?");
                $check_stmt->execute([$product, $product_id]);
                if ($check_stmt->rowCount() > 0) {
                    $message = 'Product name already exists!';
                    $messageType = 'error';
                } else {
                    $update_stmt = $pdo->prepare("
                        UPDATE products SET product = ?, category = ?, stock = ?, price = ?, status = ?, package = ?, description = ?, updated_at = NOW()
                        WHERE id = ?
                    ");
                    $result = $update_stmt->execute([$product, $category, $stock, $price, $status, $package, $description, $product_id]);

                    if ($result) {
                        $message = 'Product updated successfully!';
                        $messageType = 'success';
                    } else {
                        $message = 'Error updating product. Please try again.';
                        $messageType = 'error';
                    }
                }
            }
        } else {
            // Handle add new product
            $product = trim($_POST['product'] ?? '');
            $category = trim($_POST['category'] ?? '');
            $stock = filter_var($_POST['stock'] ?? 0, FILTER_VALIDATE_INT);
            $price = filter_var($_POST['price'] ?? 0.0, FILTER_VALIDATE_FLOAT);
            $status = trim($_POST['status'] ?? '');
            $package = trim($_POST['package'] ?? '');
            $description = trim($_POST['description'] ?? '');

            // Server-side validation
            $errors = [];
            if (empty($product)) $errors[] = 'Product name is required';
            if (empty($category)) $errors[] = 'Category is required';
            if ($stock === false || $stock < 0) $errors[] = 'Valid stock quantity is required';
            if ($price === false || $price < 0) $errors[] = 'Valid price is required';
            if (empty($status)) $errors[] = 'Status is required';

            // Validate status
            $valid_statuses = ['In Stock', 'Low Stock', 'Out of Stock'];
            if (!empty($status) && !in_array($status, $valid_statuses)) {
                $errors[] = 'Invalid status';
            }

            if (!empty($errors)) {
                $message = implode(', ', $errors);
                $messageType = 'error';
            } else {
                // Check for duplicate product name
                $check_stmt = $pdo->prepare("SELECT id FROM products WHERE product = ?");
                $check_stmt->execute([$product]);
                if ($check_stmt->rowCount() > 0) {
                    $message = 'Product name already exists!';
                    $messageType = 'error';
                } else {
                    // Insert new product using prepared statement
                    $insert_stmt = $pdo->prepare("
                        INSERT INTO products (product, category, stock, price, status, package, description, created, updated_at)
                        VALUES (?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
                    ");

                    $result = $insert_stmt->execute([
                        $product,
                        $category,
                        $stock,
                        $price,
                        $status,
                        $package,
                        $description
                    ]);

                    if ($result) {
                        $message = 'Product added successfully!';
                        $messageType = 'success';
                    } else {
                        $message = 'Error adding product. Please try again.';
                        $messageType = 'error';
                    }
                }
            }
        }

    } catch (PDOException $e) {
        // Log the actual error for debugging (don't show to user)
        error_log("Database error: " . $e->getMessage());
        $message = 'A database error occurred. Please try again later.';
        $messageType = 'error';
    } catch (Exception $e) {
        // Log any other errors
        error_log("General error: " . $e->getMessage());
        $message = 'An error occurred. Please try again.';
        $messageType = 'error';
    }
}
?>

<?php include("include/header.php") ?>
<body class="bg-gray-100" x-data="{ sidebarOpen: false }">
    <div class="flex h-screen">
        <!-- Sidebar -->
        <?php include("include/sidebar.php") ?>

        <!-- Main Content -->
        <div class="flex-1 flex flex-col overflow-hidden">
            <!-- Top Navigation -->
            <?php include("include/top_navbar.php") ?>

            <!-- Page Content -->
            <main class="flex-1 overflow-y-auto p-6">
                <!-- Page Header -->
                <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
                    <div>
                        <h1 class="text-2xl font-bold text-gray-900">Product Management</h1>
                        <p class="mt-1 text-sm text-gray-600">Manage your products and inventory</p>
                    </div>
                    <div class="mt-4 md:mt-0 space-x-2">
                        <button id="addProductBtn" onclick="openAddModal()" class="bg-indigo-600 text-white px-4 py-2 rounded-lg hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            <i class="fas fa-plus mr-2"></i> Add Product
                        </button>
                        <!-- Debug button - remove after testing -->
                        <button onclick="testModal()" class="bg-green-600 text-white px-3 py-2 rounded-lg hover:bg-green-700 text-sm">
                            Test Modal
                        </button>
                    </div>
                </div>

                <!-- Success/Error Messages -->
                <?php if (!empty($message)): ?>
                <div class="mb-6">
                    <div class="<?php echo $messageType == 'success' ? 'bg-green-100 border border-green-400 text-green-700' : 'bg-red-100 border border-red-400 text-red-700'; ?> px-4 py-3 rounded relative" role="alert">
                        <strong class="font-bold"><?php echo $messageType == 'success' ? 'Success!' : 'Error!'; ?></strong>
                        <span class="block sm:inline"><?php echo htmlspecialchars($message); ?></span>
                        <span class="absolute top-0 bottom-0 right-0 px-4 py-3">
                            <svg class="fill-current h-6 w-6 text-<?php echo $messageType == 'success' ? 'green' : 'red'; ?>-500" role="button" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" onclick="this.parentElement.parentElement.style.display='none';">
                                <title>Close</title>
                                <path d="M14.348 14.849a1.2 1.2 0 0 1-1.697 0L10 11.819l-2.651 3.029a1.2 1.2 0 1 1-1.697-1.697l2.758-3.15-2.759-3.152a1.2 1.2 0 1 1 1.697-1.697L10 8.183l2.651-3.031a1.2 1.2 0 1 1 1.697 1.697l-2.758 3.152 2.758 3.15a1.2 1.2 0 0 1 0 1.698z"/>
                            </svg>
                        </span>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Product Stats -->
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                    <?php
                    // Get real product statistics from database
                    try {
                        $total_products = $pdo->query("SELECT COUNT(*) as count FROM products")->fetch()['count'] ?? 0;
                        $in_stock = $pdo->query("SELECT COUNT(*) as count FROM products WHERE status = 'In Stock'")->fetch()['count'] ?? 0;
                        $low_stock = $pdo->query("SELECT COUNT(*) as count FROM products WHERE status = 'Low Stock'")->fetch()['count'] ?? 0;
                        $out_of_stock = $pdo->query("SELECT COUNT(*) as count FROM products WHERE status = 'Out of Stock'")->fetch()['count'] ?? 0;
                    } catch (Exception $e) {
                        $total_products = $in_stock = $low_stock = $out_of_stock = 0;
                    }
                    ?>
                    <div class="bg-white p-4 rounded-lg shadow">
                        <div class="flex items-center">
                            <div class="p-3 rounded-full bg-blue-100 text-blue-600">
                                <i class="fas fa-box"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm text-gray-500">Total Products</p>
                                <h3 class="text-xl font-semibold"><?php echo number_format($total_products); ?></h3>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white p-4 rounded-lg shadow">
                        <div class="flex items-center">
                            <div class="p-3 rounded-full bg-green-100 text-green-600">
                                <i class="fas fa-check-circle"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm text-gray-500">In Stock</p>
                                <h3 class="text-xl font-semibold"><?php echo number_format($in_stock); ?></h3>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white p-4 rounded-lg shadow">
                        <div class="flex items-center">
                            <div class="p-3 rounded-full bg-yellow-100 text-yellow-600">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm text-gray-500">Low Stock</p>
                                <h3 class="text-xl font-semibold"><?php echo number_format($low_stock); ?></h3>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white p-4 rounded-lg shadow">
                        <div class="flex items-center">
                            <div class="p-3 rounded-full bg-red-100 text-red-600">
                                <i class="fas fa-times-circle"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm text-gray-500">Out of Stock</p>
                                <h3 class="text-xl font-semibold"><?php echo number_format($out_of_stock); ?></h3>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Filters -->
                <div class="bg-white rounded-lg shadow p-4 mb-6">
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Category</label>
                            <select class="w-full border rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                                <option>All Categories</option>
                                <option>Beef</option>
                                <option>Chicken</option>
                                <option>Pork</option>
                                <option>Lamb</option>
                                <option>Fish</option>
                                <option>Other</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                            <select class="w-full border rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                                <option>All Status</option>
                                <option>In Stock</option>
                                <option>Low Stock</option>
                                <option>Out of Stock</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Price Range</label>
                            <select class="w-full border rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                                <option>All Prices</option>
                                <option>Under $10</option>
                                <option>$10 - $25</option>
                                <option>$25 - $50</option>
                                <option>Over $50</option>
                            </select>
                        </div>
                        <div class="flex items-end">
                            <button class="w-full bg-indigo-600 text-white px-4 py-2 rounded-lg hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                Apply Filters
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Products Table -->
                <div class="bg-white rounded-lg shadow overflow-hidden">
                    <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                        <h3 class="text-lg font-medium text-gray-900">All Products</h3>
                        <div class="flex items-center space-x-2">
                            <button onclick="exportProducts()" class="text-gray-500 hover:text-gray-700" title="Export Products">
                                <i class="fas fa-file-export"></i>
                            </button>
                            <button onclick="printProducts()" class="text-gray-500 hover:text-gray-700" title="Print Products">
                                <i class="fas fa-print"></i>
                            </button>
                        </div>
                    </div>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Product</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Stock</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Price</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <?php
                                try {
                                    // Fetch products from database
                                    $products_query = "SELECT id, product, category, stock, price, status, package, description, created FROM products ORDER BY created DESC LIMIT 10";
                                    $products_result = $pdo->query($products_query);
                                    $products = $products_result->fetchAll();

                                    if (count($products) > 0) {
                                        foreach ($products as $product_item):
                                ?>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <div class="flex-shrink-0 h-10 w-10">
                                                <div class="h-10 w-10 rounded-md bg-indigo-100 flex items-center justify-center">
                                                    <span class="text-indigo-600 font-medium"><?php echo strtoupper(substr($product_item['product'], 0, 2)); ?></span>
                                                </div>
                                            </div>
                                            <div class="ml-4">
                                                <div class="text-sm font-medium text-gray-900"><?php echo htmlspecialchars($product_item['product']); ?></div>
                                                <div class="text-sm text-gray-500">ID: #<?php echo htmlspecialchars($product_item['id']); ?></div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900"><?php echo htmlspecialchars($product_item['category']); ?></div>
                                        <div class="text-sm text-gray-500"><?php echo htmlspecialchars($product_item['package'] ?: 'N/A'); ?></div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900"><?php echo number_format($product_item['stock']); ?></div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900">$<?php echo number_format($product_item['price'], 2); ?></div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <?php
                                        $status_colors = [
                                            'In Stock' => 'bg-green-100 text-green-800',
                                            'Low Stock' => 'bg-yellow-100 text-yellow-800',
                                            'Out of Stock' => 'bg-red-100 text-red-800'
                                        ];
                                        $color_class = $status_colors[$product_item['status']] ?? 'bg-gray-100 text-gray-800';
                                        ?>
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full <?php echo $color_class; ?>">
                                            <?php echo htmlspecialchars($product_item['status']); ?>
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                        <a href="#" onclick="viewProduct(<?php echo $product_item['id']; ?>)" class="text-blue-600 hover:text-blue-900 mr-3" title="View Product">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="#" onclick="editProduct(<?php echo $product_item['id']; ?>)" class="text-indigo-600 hover:text-indigo-900 mr-3" title="Edit Product">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="#" onclick="deleteProduct(<?php echo $product_item['id']; ?>)" class="text-red-600 hover:text-red-900" title="Delete Product">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    </td>
                                </tr>
                                <?php
                                        endforeach;
                                    } else {
                                ?>
                                <tr>
                                    <td colspan="6" class="px-6 py-4 text-center text-gray-500">
                                        <div class="flex flex-col items-center">
                                            <i class="fas fa-box text-4xl text-gray-300 mb-2"></i>
                                            <p class="text-lg font-medium">No products found</p>
                                            <p class="text-sm">Click "Add Product" to create your first product</p>
                                        </div>
                                    </td>
                                </tr>
                                <?php
                                    }
                                } catch (Exception $e) {
                                    error_log("Error fetching products: " . $e->getMessage());
                                ?>
                                <tr>
                                    <td colspan="6" class="px-6 py-4 text-center text-red-500">
                                        <div class="flex flex-col items-center">
                                            <i class="fas fa-exclamation-triangle text-4xl text-red-300 mb-2"></i>
                                            <p class="text-lg font-medium">Error loading products</p>
                                            <p class="text-sm">Please try refreshing the page</p>
                                        </div>
                                    </td>
                                </tr>
                                <?php
                                }
                                ?>
                            </tbody>
                        </table>
                    </div>
                    <!-- Pagination -->
                    <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
                        <div class="flex-1 flex justify-between sm:hidden">
                            <a href="#" class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                                Previous
                            </a>
                            <a href="#" class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                                Next
                            </a>
                        </div>
                        <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                            <div>
                                <p class="text-sm text-gray-700">
                                    <?php
                                    $displayed_count = count($products ?? []);
                                    $total_count = $total_products ?? 0;
                                    ?>
                                    Showing <span class="font-medium">1</span> to <span class="font-medium"><?php echo $displayed_count; ?></span> of <span class="font-medium"><?php echo $total_count; ?></span> results
                                </p>
                            </div>
                            <div>
                                <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                                    <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                        <span class="sr-only">Previous</span>
                                        <i class="fas fa-chevron-left h-5 w-5"></i>
                                    </a>
                                    <a href="#" aria-current="page" class="z-10 bg-indigo-50 border-indigo-500 text-indigo-600 relative inline-flex items-center px-4 py-2 border text-sm font-medium">
                                        1
                                    </a>
                                    <a href="#" class="bg-white border-gray-300 text-gray-500 hover:bg-gray-50 relative inline-flex items-center px-4 py-2 border text-sm font-medium">
                                        2
                                    </a>
                                    <a href="#" class="bg-white border-gray-300 text-gray-500 hover:bg-gray-50 relative inline-flex items-center px-4 py-2 border text-sm font-medium">
                                        3
                                    </a>
                                    <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                        <span class="sr-only">Next</span>
                                        <i class="fas fa-chevron-right h-5 w-5"></i>
                                    </a>
                                </nav>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Add Product Modal -->
    <div id="addProductModal" class="fixed inset-0 z-50 overflow-y-auto hidden" aria-labelledby="modal-title" role="dialog" aria-modal="true">
        <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true"></div>
            <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

            <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-2xl sm:w-full">
                <form method="POST" action="">
                    <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                        <div class="sm:flex sm:items-start">
                            <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                                <div class="flex justify-between items-center">
                                    <h3 class="text-lg leading-6 font-medium text-gray-900" id="modal-title">
                                        Add New Product
                                    </h3>
                                    <button type="button" onclick="closeAddModal()" class="text-gray-400 hover:text-gray-500 focus:outline-none">
                                        <i class="fas fa-times text-xl"></i>
                                    </button>
                                </div>

                                <div class="mt-5 space-y-6">
                                    <div class="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                                        <!-- Product Name -->
                                        <div class="sm:col-span-4">
                                            <label for="product" class="block text-sm font-medium text-gray-700">Product Name *</label>
                                            <div class="mt-1">
                                                <input type="text" name="product" id="product" required class="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md p-2 border">
                                            </div>
                                        </div>

                                        <!-- Category -->
                                        <div class="sm:col-span-2">
                                            <label for="category" class="block text-sm font-medium text-gray-700">Category *</label>
                                            <select id="category" name="category" required class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md border">
                                                <option value="">Select category</option>
                                                <option value="Beef">Beef</option>
                                                <option value="Chicken">Chicken</option>
                                                <option value="Pork">Pork</option>
                                                <option value="Lamb">Lamb</option>
                                                <option value="Fish">Fish</option>
                                                <option value="Other">Other</option>
                                            </select>
                                        </div>

                                        <!-- Stock -->
                                        <div class="sm:col-span-2">
                                            <label for="stock" class="block text-sm font-medium text-gray-700">Stock Quantity *</label>
                                            <div class="mt-1">
                                                <input type="number" name="stock" id="stock" min="0" required class="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md p-2 border">
                                            </div>
                                        </div>

                                        <!-- Price -->
                                        <div class="sm:col-span-2">
                                            <label for="price" class="block text-sm font-medium text-gray-700">Price *</label>
                                            <div class="mt-1 relative rounded-md shadow-sm">
                                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                                    <span class="text-gray-500 sm:text-sm">$</span>
                                                </div>
                                                <input type="number" name="price" id="price" step="0.01" min="0" required class="focus:ring-indigo-500 focus:border-indigo-500 block w-full pl-7 pr-12 sm:text-sm border-gray-300 rounded-md p-2 border" placeholder="0.00">
                                            </div>
                                        </div>

                                        <!-- Status -->
                                        <div class="sm:col-span-2">
                                            <label for="status" class="block text-sm font-medium text-gray-700">Status *</label>
                                            <select id="status" name="status" required class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md border">
                                                <option value="">Select status</option>
                                                <option value="In Stock">In Stock</option>
                                                <option value="Low Stock">Low Stock</option>
                                                <option value="Out of Stock">Out of Stock</option>
                                            </select>
                                        </div>

                                        <!-- Package -->
                                        <div class="sm:col-span-6">
                                            <label for="package" class="block text-sm font-medium text-gray-700">Package Details</label>
                                            <div class="mt-1">
                                                <input type="text" name="package" id="package" class="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md p-2 border" placeholder="e.g., 1kg, 500g, per piece">
                                            </div>
                                        </div>

                                        <!-- Description -->
                                        <div class="sm:col-span-6">
                                            <label for="description" class="block text-sm font-medium text-gray-700">Description</label>
                                            <div class="mt-1">
                                                <textarea id="description" name="description" rows="3" class="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border border-gray-300 rounded-md p-2" placeholder="Product description..."></textarea>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                        <button type="submit" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-indigo-600 text-base font-medium text-white hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:ml-3 sm:w-auto sm:text-sm">
                            Add Product
                        </button>
                        <button type="button" onclick="closeAddModal()" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
                            Cancel
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- View Product Modal -->
    <div id="viewProductModal" class="fixed inset-0 z-50 overflow-y-auto hidden" aria-labelledby="view-modal-title" role="dialog" aria-modal="true">
        <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true"></div>
            <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

            <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-3xl sm:w-full">
                <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                    <div class="sm:flex sm:items-start">
                        <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                            <div class="flex justify-between items-center mb-4">
                                <h3 class="text-lg leading-6 font-medium text-gray-900" id="view-modal-title">
                                    <i class="fas fa-eye text-blue-600 mr-2"></i>Product Details
                                </h3>
                                <button type="button" onclick="closeViewModal()" class="text-gray-400 hover:text-gray-500 focus:outline-none">
                                    <i class="fas fa-times text-xl"></i>
                                </button>
                            </div>

                            <!-- Product Details Content -->
                            <div id="productDetailsContent" class="space-y-6">
                                <!-- Loading state -->
                                <div id="loadingState" class="text-center py-8">
                                    <i class="fas fa-spinner fa-spin text-3xl text-gray-400 mb-4"></i>
                                    <p class="text-gray-500">Loading product details...</p>
                                </div>

                                <!-- Product details will be loaded here -->
                                <div id="productDetails" class="hidden">
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                        <!-- Left Column -->
                                        <div class="space-y-4">
                                            <div class="bg-gray-50 p-4 rounded-lg">
                                                <div class="flex items-center mb-3">
                                                    <div id="productAvatar" class="h-16 w-16 rounded-lg bg-indigo-100 flex items-center justify-center mr-4">
                                                        <span id="productInitials" class="text-indigo-600 font-bold text-xl"></span>
                                                    </div>
                                                    <div>
                                                        <h4 id="productName" class="text-xl font-semibold text-gray-900"></h4>
                                                        <p id="productId" class="text-sm text-gray-500"></p>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="space-y-3">
                                                <div class="flex justify-between items-center py-2 border-b border-gray-200">
                                                    <span class="text-sm font-medium text-gray-500">Category:</span>
                                                    <span id="productCategory" class="text-sm text-gray-900 font-medium"></span>
                                                </div>
                                                <div class="flex justify-between items-center py-2 border-b border-gray-200">
                                                    <span class="text-sm font-medium text-gray-500">Stock Quantity:</span>
                                                    <span id="productStock" class="text-sm text-gray-900 font-medium"></span>
                                                </div>
                                                <div class="flex justify-between items-center py-2 border-b border-gray-200">
                                                    <span class="text-sm font-medium text-gray-500">Price:</span>
                                                    <span id="productPrice" class="text-sm text-gray-900 font-medium"></span>
                                                </div>
                                                <div class="flex justify-between items-center py-2 border-b border-gray-200">
                                                    <span class="text-sm font-medium text-gray-500">Status:</span>
                                                    <span id="productStatus" class="text-sm font-medium"></span>
                                                </div>
                                                <div class="flex justify-between items-center py-2 border-b border-gray-200">
                                                    <span class="text-sm font-medium text-gray-500">Package:</span>
                                                    <span id="productPackage" class="text-sm text-gray-900"></span>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Right Column -->
                                        <div class="space-y-4">
                                            <div>
                                                <h5 class="text-sm font-medium text-gray-500 mb-2">Description:</h5>
                                                <div id="productDescription" class="bg-gray-50 p-3 rounded-lg text-sm text-gray-700 min-h-[100px]"></div>
                                            </div>

                                            <div class="space-y-3">
                                                <div class="flex justify-between items-center py-2 border-b border-gray-200">
                                                    <span class="text-sm font-medium text-gray-500">Created:</span>
                                                    <span id="productCreated" class="text-sm text-gray-900"></span>
                                                </div>
                                                <div class="flex justify-between items-center py-2 border-b border-gray-200">
                                                    <span class="text-sm font-medium text-gray-500">Last Updated:</span>
                                                    <span id="productUpdated" class="text-sm text-gray-900"></span>
                                                </div>
                                            </div>

                                            <!-- Stock Status Indicator -->
                                            <div class="bg-gray-50 p-4 rounded-lg">
                                                <h5 class="text-sm font-medium text-gray-500 mb-2">Stock Status:</h5>
                                                <div id="stockIndicator" class="flex items-center">
                                                    <div id="stockBar" class="flex-1 bg-gray-200 rounded-full h-2 mr-3">
                                                        <div id="stockProgress" class="h-2 rounded-full transition-all duration-300"></div>
                                                    </div>
                                                    <span id="stockPercentage" class="text-xs font-medium"></span>
                                                </div>
                                                <p id="stockMessage" class="text-xs text-gray-500 mt-1"></p>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Error state -->
                                <div id="errorState" class="hidden text-center py-8">
                                    <i class="fas fa-exclamation-triangle text-3xl text-red-400 mb-4"></i>
                                    <p class="text-red-500">Error loading product details. Please try again.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                    <button type="button" onclick="editProductFromView()" id="editFromViewBtn" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-indigo-600 text-base font-medium text-white hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:ml-3 sm:w-auto sm:text-sm">
                        <i class="fas fa-edit mr-2"></i>Edit Product
                    </button>
                    <button type="button" onclick="printProductDetails()" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
                        <i class="fas fa-print mr-2"></i>Print
                    </button>
                    <button type="button" onclick="closeViewModal()" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
                        Close
                    </button>
                </div>
            </div>
        </div>
    </div>

    <?php include("include/footer.php") ?>

    <style>
        /* Modal fixes */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-content {
            background: white;
            border-radius: 8px;
            max-width: 90vw;
            max-height: 90vh;
            overflow-y: auto;
            position: relative;
            z-index: 1001;
        }

        .hidden {
            display: none !important;
        }

        /* Ensure buttons are clickable */
        button, .btn {
            pointer-events: auto !important;
            cursor: pointer !important;
        }

        /* Fix any potential overlay issues */
        #addProductModal, #viewProductModal {
            pointer-events: auto;
        }

        #addProductModal.hidden, #viewProductModal.hidden {
            pointer-events: none;
        }
    </style>

    <script>
        // Modal functions - Simplified and more reliable
        function openAddModal() {
            console.log('Opening add modal...');
            try {
                const modal = document.getElementById('addProductModal');
                if (modal) {
                    modal.classList.remove('hidden');
                    modal.style.display = 'block';
                    document.body.style.overflow = 'hidden'; // Prevent background scroll

                    // Focus on first input after a short delay
                    setTimeout(() => {
                        const firstInput = modal.querySelector('input[name="product"]');
                        if (firstInput) {
                            firstInput.focus();
                        }
                    }, 200);

                    console.log('Add modal opened successfully');
                } else {
                    console.error('Add modal element not found');
                }
            } catch (error) {
                console.error('Error opening add modal:', error);
                alert('Error opening add product form. Please refresh the page and try again.');
            }
        }

        function closeAddModal() {
            console.log('Closing add modal...');
            try {
                const modal = document.getElementById('addProductModal');
                if (modal) {
                    modal.classList.add('hidden');
                    modal.style.display = 'none';
                    document.body.style.overflow = ''; // Restore scroll

                    // Reset form
                    const form = modal.querySelector('form');
                    if (form) {
                        form.reset();
                    }

                    console.log('Add modal closed successfully');
                }
            } catch (error) {
                console.error('Error closing add modal:', error);
            }
        }

        // View Product Modal functions
        function openViewModal() {
            console.log('Opening view modal...');
            try {
                const modal = document.getElementById('viewProductModal');
                if (modal) {
                    modal.classList.remove('hidden');
                    modal.style.display = 'block';
                    document.body.style.overflow = 'hidden';
                    console.log('View modal opened successfully');
                } else {
                    console.error('View modal element not found');
                }
            } catch (error) {
                console.error('Error opening view modal:', error);
            }
        }

        function closeViewModal() {
            console.log('Closing view modal...');
            try {
                const modal = document.getElementById('viewProductModal');
                if (modal) {
                    modal.classList.add('hidden');
                    modal.style.display = 'none';
                    document.body.style.overflow = '';

                    // Reset modal content
                    const loadingState = document.getElementById('loadingState');
                    const productDetails = document.getElementById('productDetails');
                    const errorState = document.getElementById('errorState');

                    if (loadingState) loadingState.classList.remove('hidden');
                    if (productDetails) productDetails.classList.add('hidden');
                    if (errorState) errorState.classList.add('hidden');

                    console.log('View modal closed successfully');
                }
            } catch (error) {
                console.error('Error closing view modal:', error);
            }
        }

        // Product actions
        function viewProduct(id) {
            // Open the modal
            openViewModal();

            // Show loading state
            document.getElementById('loadingState').classList.remove('hidden');
            document.getElementById('productDetails').classList.add('hidden');
            document.getElementById('errorState').classList.add('hidden');

            // Fetch product details via AJAX
            fetch(`get_product_details.php?id=${id}`)
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.success && data.product) {
                        populateProductDetails(data.product);
                        // Hide loading, show details
                        document.getElementById('loadingState').classList.add('hidden');
                        document.getElementById('productDetails').classList.remove('hidden');
                        document.getElementById('errorState').classList.add('hidden');
                    } else {
                        throw new Error(data.error || 'Failed to load product details');
                    }
                })
                .catch(error => {
                    console.error('Error fetching product details:', error);
                    // Hide loading, show error
                    document.getElementById('loadingState').classList.add('hidden');
                    document.getElementById('productDetails').classList.add('hidden');
                    document.getElementById('errorState').classList.remove('hidden');
                });
        }

        function populateProductDetails(product) {
            // Set product initials and name
            document.getElementById('productInitials').textContent = product.initials;
            document.getElementById('productName').textContent = product.product;
            document.getElementById('productId').textContent = `Product ID: #${product.id}`;

            // Set basic details
            document.getElementById('productCategory').textContent = product.category;
            document.getElementById('productStock').textContent = product.formatted_stock;
            document.getElementById('productPrice').textContent = product.formatted_price;
            document.getElementById('productPackage').textContent = product.package;
            document.getElementById('productDescription').textContent = product.description;
            document.getElementById('productCreated').textContent = product.formatted_created;
            document.getElementById('productUpdated').textContent = product.formatted_updated;

            // Set status with color
            const statusElement = document.getElementById('productStatus');
            statusElement.textContent = product.status;
            statusElement.className = `text-sm font-medium px-2 py-1 rounded-full ${product.status_color}`;

            // Set stock progress bar
            const stockProgress = document.getElementById('stockProgress');
            const stockPercentage = document.getElementById('stockPercentage');
            const stockMessage = document.getElementById('stockMessage');

            stockProgress.style.width = `${product.stock_percentage}%`;
            stockProgress.className = `h-2 rounded-full transition-all duration-300 ${product.stock_color}`;
            stockPercentage.textContent = `${Math.round(product.stock_percentage)}%`;
            stockMessage.textContent = product.stock_message;

            // Store product ID for edit functionality
            document.getElementById('editFromViewBtn').setAttribute('data-product-id', product.id);
        }

        function editProductFromView() {
            const productId = document.getElementById('editFromViewBtn').getAttribute('data-product-id');
            if (productId) {
                closeViewModal();
                // You can implement edit functionality here
                editProduct(productId);
            }
        }

        function printProductDetails() {
            // Create a printable version of the product details
            const productName = document.getElementById('productName').textContent;
            const productId = document.getElementById('productId').textContent;
            const category = document.getElementById('productCategory').textContent;
            const stock = document.getElementById('productStock').textContent;
            const price = document.getElementById('productPrice').textContent;
            const status = document.getElementById('productStatus').textContent;
            const packageInfo = document.getElementById('productPackage').textContent;
            const description = document.getElementById('productDescription').textContent;
            const created = document.getElementById('productCreated').textContent;
            const updated = document.getElementById('productUpdated').textContent;

            const printWindow = window.open('', '_blank');
            printWindow.document.write(`
                <!DOCTYPE html>
                <html>
                <head>
                    <title>Product Details - ${productName}</title>
                    <style>
                        body { font-family: Arial, sans-serif; margin: 20px; }
                        .header { text-align: center; margin-bottom: 30px; }
                        .details { margin-bottom: 20px; }
                        .row { display: flex; margin-bottom: 10px; }
                        .label { font-weight: bold; width: 150px; }
                        .value { flex: 1; }
                        .description { margin-top: 20px; }
                        @media print { body { margin: 0; } }
                    </style>
                </head>
                <body>
                    <div class="header">
                        <h1>Product Details</h1>
                        <h2>${productName}</h2>
                        <p>${productId}</p>
                    </div>
                    <div class="details">
                        <div class="row"><div class="label">Category:</div><div class="value">${category}</div></div>
                        <div class="row"><div class="label">Stock Quantity:</div><div class="value">${stock}</div></div>
                        <div class="row"><div class="label">Price:</div><div class="value">${price}</div></div>
                        <div class="row"><div class="label">Status:</div><div class="value">${status}</div></div>
                        <div class="row"><div class="label">Package:</div><div class="value">${packageInfo}</div></div>
                        <div class="row"><div class="label">Created:</div><div class="value">${created}</div></div>
                        <div class="row"><div class="label">Last Updated:</div><div class="value">${updated}</div></div>
                    </div>
                    <div class="description">
                        <div class="label">Description:</div>
                        <p>${description}</p>
                    </div>
                    <script>window.print(); window.close();</script>
                </body>
                </html>
            `);
            printWindow.document.close();
        }

        function editProduct(id) {
            // Implement edit product functionality
            alert('Edit product ID: ' + id);
        }

        function deleteProduct(id) {
            if (confirm('Are you sure you want to delete this product?')) {
                // Implement delete functionality
                alert('Delete product ID: ' + id);
            }
        }

        function exportProducts() {
            // Implement export functionality
            alert('Export products functionality');
        }

        function printProducts() {
            // Implement print functionality
            window.print();
        }

        // Test function to debug modal issues
        function testModal() {
            console.log('Test modal function called');
            alert('Test button clicked! Now testing modal...');

            // Test if elements exist
            const addModal = document.getElementById('addProductModal');
            const viewModal = document.getElementById('viewProductModal');
            const addBtn = document.getElementById('addProductBtn');

            console.log('Add Modal exists:', !!addModal);
            console.log('View Modal exists:', !!viewModal);
            console.log('Add Button exists:', !!addBtn);

            if (addModal) {
                console.log('Add Modal classes:', addModal.className);
                console.log('Add Modal style display:', addModal.style.display);
            }

            // Try to open the add modal
            try {
                openAddModal();
                console.log('openAddModal() called successfully');
            } catch (error) {
                console.error('Error calling openAddModal():', error);
                alert('Error opening modal: ' + error.message);
            }
        }

        // Event listeners
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM loaded, setting up event listeners...');

            // Add Product button
            const addBtn = document.getElementById('addProductBtn');
            if (addBtn) {
                addBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    console.log('Add button clicked');
                    openAddModal();
                });
                console.log('Add button listener attached');
            } else {
                console.error('Add button not found');
            }

            // Close modals when clicking outside
            const addModal = document.getElementById('addProductModal');
            if (addModal) {
                addModal.addEventListener('click', function(e) {
                    if (e.target === this) {
                        closeAddModal();
                    }
                });
            }

            const viewModal = document.getElementById('viewProductModal');
            if (viewModal) {
                viewModal.addEventListener('click', function(e) {
                    if (e.target === this) {
                        closeViewModal();
                    }
                });
            }

            // Form validation
            const form = document.querySelector('#addProductModal form');
            if (form) {
                form.addEventListener('submit', function(e) {
                    const product = document.getElementById('product').value.trim();
                    const category = document.getElementById('category').value;
                    const stock = document.getElementById('stock').value;
                    const price = document.getElementById('price').value;
                    const status = document.getElementById('status').value;

                    if (!product || !category || !stock || !price || !status) {
                        alert('Please fill in all required fields (*).');
                        e.preventDefault();
                        return false;
                    }

                    if (parseFloat(price) < 0) {
                        alert('Price cannot be negative.');
                        e.preventDefault();
                        return false;
                    }

                    if (parseInt(stock) < 0) {
                        alert('Stock cannot be negative.');
                        e.preventDefault();
                        return false;
                    }
                });
            }
        });
    </script>
</body>
</html>
