<?php
require_once 'database/conn.php';
require_once 'check_session.php';

// Require user to be logged in
requireLogin();

// Set content type to JSON
header('Content-Type: application/json');

// Check if product ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    http_response_code(400);
    echo json_encode(['error' => 'Product ID is required']);
    exit();
}

$product_id = intval($_GET['id']);

try {
    // Fetch product details from database
    $stmt = $pdo->prepare("
        SELECT id, product, category, stock, price, status, package, description, created, updated_at
        FROM products 
        WHERE id = ?
    ");
    
    $stmt->execute([$product_id]);
    $product = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$product) {
        http_response_code(404);
        echo json_encode(['error' => 'Product not found']);
        exit();
    }
    
    // Format the data for display
    $response = [
        'success' => true,
        'product' => [
            'id' => $product['id'],
            'product' => $product['product'],
            'category' => $product['category'],
            'stock' => intval($product['stock']),
            'price' => floatval($product['price']),
            'status' => $product['status'],
            'package' => $product['package'] ?: 'Not specified',
            'description' => $product['description'] ?: 'No description available',
            'created' => $product['created'],
            'updated_at' => $product['updated_at'],
            'formatted_price' => '$' . number_format(floatval($product['price']), 2),
            'formatted_stock' => number_format(intval($product['stock'])),
            'formatted_created' => date('M j, Y g:i A', strtotime($product['created'])),
            'formatted_updated' => date('M j, Y g:i A', strtotime($product['updated_at'])),
            'initials' => strtoupper(substr($product['product'], 0, 2))
        ]
    ];
    
    // Calculate stock status for progress bar
    $stock = intval($product['stock']);
    $status = $product['status'];
    
    // Determine stock level and color based on status and quantity
    if ($status === 'Out of Stock' || $stock === 0) {
        $stock_percentage = 0;
        $stock_color = 'bg-red-500';
        $stock_message = 'Product is out of stock';
    } elseif ($status === 'Low Stock' || $stock <= 10) {
        $stock_percentage = min(($stock / 50) * 100, 30); // Cap at 30% for low stock
        $stock_color = 'bg-yellow-500';
        $stock_message = 'Stock is running low';
    } else {
        // In Stock
        if ($stock <= 25) {
            $stock_percentage = 50;
        } elseif ($stock <= 50) {
            $stock_percentage = 75;
        } else {
            $stock_percentage = 100;
        }
        $stock_color = 'bg-green-500';
        $stock_message = 'Stock level is good';
    }
    
    $response['product']['stock_percentage'] = $stock_percentage;
    $response['product']['stock_color'] = $stock_color;
    $response['product']['stock_message'] = $stock_message;
    
    // Add status color class
    $status_colors = [
        'In Stock' => 'bg-green-100 text-green-800',
        'Low Stock' => 'bg-yellow-100 text-yellow-800',
        'Out of Stock' => 'bg-red-100 text-red-800'
    ];
    $response['product']['status_color'] = $status_colors[$status] ?? 'bg-gray-100 text-gray-800';
    
    echo json_encode($response);
    
} catch (PDOException $e) {
    error_log("Database error in get_product_details.php: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => 'Database error occurred']);
} catch (Exception $e) {
    error_log("General error in get_product_details.php: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => 'An error occurred while fetching product details']);
}
?>
