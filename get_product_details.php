<?php
require_once 'database/conn.php'; // Adjust path if needed
// No session check needed here usually, as it's an API endpoint
// However, you might want to add authentication/authorization if sensitive

header('Content-Type: application/json');

if (!isset($_GET['id'])) {
    echo json_encode(['error' => 'Product ID not provided.']);
    exit;
}

$productId = intval($_GET['id']);

if ($productId <= 0) {
    echo json_encode(['error' => 'Invalid Product ID.']);
    exit;
}

try {
    // Ensure you select all necessary fields, including 'created_at' or your equivalent date column
    $stmt = $pdo->prepare("SELECT id, product, category, stock, price, status, package, description, created_at FROM products WHERE id = ?");
    $stmt->execute([$productId]);
    $product = $stmt->fetch(PDO::FETCH_ASSOC);

    if ($product) {
        // Convert numeric types explicitly if necessary, though PDO often handles this.
        $product['stock'] = (int)$product['stock'];
        $product['price'] = (float)$product['price'];
        echo json_encode($product);
    } else {
        echo json_encode(['error' => 'Product not found.']);
    }
} catch (PDOException $e) {
    error_log("Database error in get_product_details.php: " . $e->getMessage());
    echo json_encode(['error' => 'Database error occurred.']);
} catch (Exception $e) {
    error_log("General error in get_product_details.php: " . $e->getMessage());
    echo json_encode(['error' => 'An unexpected error occurred.']);
}
?>
